<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Space Phoenix – Backend Developer Portfolio (Simple)</title>
  <meta name="description" content="Backend developer portfolio with space theme and smooth animations">
  <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🔥</text></svg>">
  <style>
    :root {
      --bg: #07070a;
      --panel: #0c0f16;
      --text: #eaeaf2;
      --muted: #9aa0a6;
      --flame: #ff6b35;
      --glow: #ffd56b;
      --accent: #6c63ff;
    }
    
    * { box-sizing: border-box; }
    
    body {
      margin: 0;
      font-family: ui-sans-serif, system-ui, -apple-system, sans-serif;
      background: radial-gradient(1200px 800px at 70% -10%, rgba(108,99,255,.15), transparent 60%),
                  radial-gradient(800px 600px at 10% 10%, rgba(255,107,53,.12), transparent 60%),
                  var(--bg);
      color: var(--text);
      line-height: 1.6;
      scroll-behavior: smooth;
    }
    
    .container { max-width: 1200px; margin: 0 auto; padding: 0 1rem; }
    
    header {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      z-index: 100;
      background: rgba(7,7,10,0.9);
      backdrop-filter: blur(10px);
      border-bottom: 1px solid rgba(255,255,255,0.1);
    }
    
    nav {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 1rem;
    }
    
    .logo {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      font-weight: 700;
      font-size: 1.1rem;
    }
    
    .logo::before {
      content: "🔥";
      font-size: 1.5rem;
    }
    
    .nav-links {
      display: flex;
      gap: 2rem;
      list-style: none;
      margin: 0;
      padding: 0;
    }
    
    .nav-links a {
      color: var(--text);
      text-decoration: none;
      transition: color 0.3s ease;
    }
    
    .nav-links a:hover {
      color: var(--glow);
    }
    
    main { margin-top: 80px; }
    
    section {
      padding: 4rem 0;
      min-height: 100vh;
      display: flex;
      align-items: center;
    }
    
    .hero {
      text-align: center;
    }
    
    .hero h1 {
      font-size: clamp(2.5rem, 5vw, 4rem);
      margin: 0 0 1rem;
      background: linear-gradient(135deg, var(--glow), var(--flame));
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }
    
    .hero p {
      font-size: 1.2rem;
      color: var(--muted);
      max-width: 600px;
      margin: 0 auto 2rem;
    }
    
    .cta {
      display: flex;
      gap: 1rem;
      justify-content: center;
      flex-wrap: wrap;
    }
    
    .btn {
      padding: 1rem 2rem;
      border: none;
      border-radius: 8px;
      font-weight: 600;
      text-decoration: none;
      transition: all 0.3s ease;
      cursor: pointer;
    }
    
    .btn-primary {
      background: linear-gradient(135deg, var(--glow), var(--flame));
      color: var(--bg);
    }
    
    .btn-secondary {
      background: transparent;
      color: var(--text);
      border: 2px solid rgba(255,255,255,0.2);
    }
    
    .btn:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(255,213,107,0.3);
    }
    
    .grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 2rem;
      margin: 2rem 0;
    }
    
    .card {
      background: var(--panel);
      border: 1px solid rgba(255,255,255,0.1);
      border-radius: 12px;
      padding: 2rem;
      transition: all 0.3s ease;
    }
    
    .card:hover {
      transform: translateY(-4px);
      border-color: rgba(255,213,107,0.3);
      box-shadow: 0 12px 40px rgba(0,0,0,0.3);
    }
    
    .card h3 {
      color: var(--glow);
      margin: 0 0 1rem;
    }
    
    .tech-stack {
      display: flex;
      flex-wrap: wrap;
      gap: 0.5rem;
      margin: 1rem 0;
    }
    
    .tech-badge {
      background: rgba(255,255,255,0.1);
      padding: 0.3rem 0.8rem;
      border-radius: 20px;
      font-size: 0.9rem;
      color: var(--glow);
    }
    
    footer {
      text-align: center;
      padding: 2rem;
      color: var(--muted);
      border-top: 1px solid rgba(255,255,255,0.1);
    }
    
    @media (max-width: 768px) {
      .nav-links { display: none; }
      section { padding: 2rem 0; min-height: auto; }
      .cta { flex-direction: column; align-items: center; }
      .btn { width: 100%; max-width: 300px; text-align: center; }
    }
    
    /* Simple animations */
    .fade-in {
      opacity: 0;
      transform: translateY(20px);
      animation: fadeIn 0.8s ease forwards;
    }
    
    @keyframes fadeIn {
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }
    
    .animate-delay-1 { animation-delay: 0.1s; }
    .animate-delay-2 { animation-delay: 0.2s; }
    .animate-delay-3 { animation-delay: 0.3s; }
  </style>
</head>
<body>
  <header>
    <nav class="container">
      <div class="logo">Space Phoenix</div>
      <ul class="nav-links">
        <li><a href="#hero">Home</a></li>
        <li><a href="#about">About</a></li>
        <li><a href="#projects">Projects</a></li>
        <li><a href="#contact">Contact</a></li>
      </ul>
    </nav>
  </header>

  <main>
    <section id="hero" class="hero">
      <div class="container">
        <h1 class="fade-in">Rising from Code. Building the Future.</h1>
        <p class="fade-in animate-delay-1">Backend Developer specializing in resilient APIs, distributed systems, and developer tooling. Obsessed with performance, observability, and elegant architectures.</p>
        <div class="cta fade-in animate-delay-2">
          <a href="#projects" class="btn btn-primary">View Projects</a>
          <a href="#contact" class="btn btn-secondary">Get in Touch</a>
        </div>
      </div>
    </section>

    <section id="about">
      <div class="container">
        <h2 class="fade-in">About Me</h2>
        <div class="grid">
          <div class="card fade-in animate-delay-1">
            <h3>🚀 Backend Expertise</h3>
            <p>I build backends that don't blink under load. Experience with Node.js, Express, NestJS, Python (FastAPI), PostgreSQL, Redis, Kafka, Docker, and Kubernetes.</p>
            <div class="tech-stack">
              <span class="tech-badge">Node.js</span>
              <span class="tech-badge">Express</span>
              <span class="tech-badge">NestJS</span>
              <span class="tech-badge">TypeScript</span>
            </div>
          </div>
          <div class="card fade-in animate-delay-2">
            <h3>🔧 Infrastructure</h3>
            <p>I care about clean boundaries, excellent DX, and making systems observable. Proficient in cloud platforms and container orchestration.</p>
            <div class="tech-stack">
              <span class="tech-badge">Docker</span>
              <span class="tech-badge">Kubernetes</span>
              <span class="tech-badge">AWS</span>
              <span class="tech-badge">PostgreSQL</span>
            </div>
          </div>
        </div>
      </div>
    </section>

    <section id="projects">
      <div class="container">
        <h2 class="fade-in">Featured Projects</h2>
        <div class="grid">
          <div class="card fade-in animate-delay-1">
            <h3>🚀 Stellar API Gateway</h3>
            <p>High-performance API gateway with advanced rate limiting, JWT authentication, and comprehensive telemetry. Handles 120k requests per second.</p>
            <div class="tech-stack">
              <span class="tech-badge">Node.js</span>
              <span class="tech-badge">Redis</span>
              <span class="tech-badge">Kubernetes</span>
            </div>
          </div>
          <div class="card fade-in animate-delay-2">
            <h3>⚡ Event Nova</h3>
            <p>Distributed event streaming platform built on Kafka with transactional outbox pattern and idempotency guarantees.</p>
            <div class="tech-stack">
              <span class="tech-badge">Kafka</span>
              <span class="tech-badge">PostgreSQL</span>
              <span class="tech-badge">Docker</span>
            </div>
          </div>
          <div class="card fade-in animate-delay-3">
            <h3>🔭 Constellation Observability</h3>
            <p>Comprehensive observability platform with OpenTelemetry integration, distributed tracing, and intelligent alerting.</p>
            <div class="tech-stack">
              <span class="tech-badge">OpenTelemetry</span>
              <span class="tech-badge">Grafana</span>
              <span class="tech-badge">Python</span>
            </div>
          </div>
        </div>
      </div>
    </section>

    <section id="contact">
      <div class="container">
        <h2 class="fade-in">Get In Touch</h2>
        <div class="card fade-in animate-delay-1" style="max-width: 600px; margin: 0 auto;">
          <p>Have a challenge that needs firepower? I'm open to collaborations and roles.</p>
          <div class="cta">
            <a href="mailto:<EMAIL>" class="btn btn-primary">Send Email</a>
            <a href="https://linkedin.com/in/spacephoenix" class="btn btn-secondary">LinkedIn</a>
          </div>
        </div>
      </div>
    </section>
  </main>

  <footer>
    <div class="container">
      <p>&copy; 2025 Space Phoenix — Built with HTML, CSS, and JavaScript</p>
    </div>
  </footer>

  <script>
    // Simple intersection observer for animations
    const observerOptions = {
      threshold: 0.1,
      rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          entry.target.style.animationPlayState = 'running';
        }
      });
    }, observerOptions);

    // Observe all fade-in elements
    document.querySelectorAll('.fade-in').forEach(el => {
      el.style.animationPlayState = 'paused';
      observer.observe(el);
    });

    // Smooth scroll for navigation links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
      anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
          target.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
          });
        }
      });
    });

    console.log('🚀 Simple Space Phoenix Portfolio loaded successfully!');
  </script>
</body>
</html>
