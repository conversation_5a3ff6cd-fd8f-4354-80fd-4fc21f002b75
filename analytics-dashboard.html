<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Portfolio Analytics Dashboard</title>
  <style>
    body {
      font-family: ui-sans-serif, system-ui, -apple-system, sans-serif;
      background: #07070a;
      color: #eaeaf2;
      margin: 0;
      padding: 2rem;
      line-height: 1.6;
    }
    
    .container {
      max-width: 1200px;
      margin: 0 auto;
    }
    
    .header {
      text-align: center;
      margin-bottom: 3rem;
    }
    
    .header h1 {
      background: linear-gradient(135deg, #ffd56b, #ff6b35);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      margin: 0;
    }
    
    .stats-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 1.5rem;
      margin-bottom: 3rem;
    }
    
    .stat-card {
      background: #0c0f16;
      border: 1px solid rgba(255,255,255,0.1);
      border-radius: 12px;
      padding: 1.5rem;
      text-align: center;
    }
    
    .stat-number {
      font-size: 2.5rem;
      font-weight: 700;
      color: #ffd56b;
      margin: 0;
    }
    
    .stat-label {
      color: #9aa0a6;
      margin: 0.5rem 0 0;
    }
    
    .chart-container {
      background: #0c0f16;
      border: 1px solid rgba(255,255,255,0.1);
      border-radius: 12px;
      padding: 1.5rem;
      margin-bottom: 2rem;
    }
    
    .chart-title {
      color: #ffd56b;
      margin: 0 0 1rem;
      font-size: 1.2rem;
    }
    
    .event-list {
      max-height: 400px;
      overflow-y: auto;
      background: #0a0d13;
      border-radius: 8px;
      padding: 1rem;
    }
    
    .event-item {
      padding: 0.5rem 0;
      border-bottom: 1px solid rgba(255,255,255,0.05);
      font-family: monospace;
      font-size: 0.9rem;
    }
    
    .event-type {
      color: #6c63ff;
      font-weight: 600;
    }
    
    .event-time {
      color: #9aa0a6;
      font-size: 0.8rem;
    }
    
    .btn {
      background: linear-gradient(135deg, #ffd56b, #ff6b35);
      color: #07070a;
      border: none;
      padding: 0.8rem 1.5rem;
      border-radius: 8px;
      font-weight: 600;
      cursor: pointer;
      margin: 0.5rem;
    }
    
    .btn:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(255,213,107,0.3);
    }
    
    .progress-bar {
      background: rgba(255,255,255,0.1);
      border-radius: 10px;
      height: 20px;
      margin: 0.5rem 0;
      overflow: hidden;
    }
    
    .progress-fill {
      background: linear-gradient(90deg, #ffd56b, #ff6b35);
      height: 100%;
      border-radius: 10px;
      transition: width 0.3s ease;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>📊 Portfolio Analytics Dashboard</h1>
      <p>Real-time visitor engagement tracking for Space Phoenix Portfolio</p>
      <button class="btn" onclick="refreshData()">🔄 Refresh Data</button>
      <button class="btn" onclick="exportData()">📥 Export Data</button>
      <button class="btn" onclick="clearData()">🗑️ Clear Data</button>
    </div>

    <div class="stats-grid" id="statsGrid">
      <!-- Stats will be populated by JavaScript -->
    </div>

    <div class="chart-container">
      <h3 class="chart-title">📈 Visitor Engagement Overview</h3>
      <div id="engagementChart">
        <!-- Chart will be populated by JavaScript -->
      </div>
    </div>

    <div class="chart-container">
      <h3 class="chart-title">📋 Recent Events</h3>
      <div class="event-list" id="eventList">
        <!-- Events will be populated by JavaScript -->
      </div>
    </div>
  </div>

  <script>
    class AnalyticsDashboard {
      constructor() {
        this.data = [];
        this.init();
      }

      init() {
        this.loadData();
        this.renderStats();
        this.renderEvents();
        this.renderEngagementChart();
        
        // Auto-refresh every 30 seconds
        setInterval(() => this.refreshData(), 30000);
      }

      loadData() {
        const stored = localStorage.getItem('portfolio_analytics');
        this.data = stored ? JSON.parse(stored) : [];
      }

      renderStats() {
        const stats = this.calculateStats();
        const statsGrid = document.getElementById('statsGrid');
        
        statsGrid.innerHTML = `
          <div class="stat-card">
            <div class="stat-number">${stats.totalEvents}</div>
            <div class="stat-label">Total Events</div>
          </div>
          <div class="stat-card">
            <div class="stat-number">${stats.uniqueSessions}</div>
            <div class="stat-label">Unique Sessions</div>
          </div>
          <div class="stat-card">
            <div class="stat-number">${stats.avgTimeOnPage}s</div>
            <div class="stat-label">Avg Time on Page</div>
          </div>
          <div class="stat-card">
            <div class="stat-number">${stats.totalInteractions}</div>
            <div class="stat-label">Total Interactions</div>
          </div>
          <div class="stat-card">
            <div class="stat-number">${stats.projectViews}</div>
            <div class="stat-label">Project Views</div>
          </div>
          <div class="stat-card">
            <div class="stat-number">${stats.avgScrollDepth}%</div>
            <div class="stat-label">Avg Scroll Depth</div>
          </div>
        `;
      }

      renderEvents() {
        const eventList = document.getElementById('eventList');
        const recentEvents = this.data.slice(-50).reverse();
        
        eventList.innerHTML = recentEvents.map(event => `
          <div class="event-item">
            <span class="event-type">${event.event}</span>
            <span class="event-time">${new Date(event.timestamp).toLocaleString()}</span>
            <div style="margin-top: 0.25rem; color: #9aa0a6;">
              ${this.formatEventDetails(event)}
            </div>
          </div>
        `).join('');
      }

      renderEngagementChart() {
        const chartContainer = document.getElementById('engagementChart');
        const deviceStats = this.getDeviceStats();
        const sectionStats = this.getSectionStats();
        
        chartContainer.innerHTML = `
          <div style="margin-bottom: 2rem;">
            <h4 style="color: #ffd56b; margin-bottom: 1rem;">Device Types</h4>
            ${Object.entries(deviceStats).map(([device, count]) => `
              <div style="margin-bottom: 0.5rem;">
                <div style="display: flex; justify-content: space-between;">
                  <span>${device}</span>
                  <span>${count} visits</span>
                </div>
                <div class="progress-bar">
                  <div class="progress-fill" style="width: ${(count / Math.max(...Object.values(deviceStats))) * 100}%"></div>
                </div>
              </div>
            `).join('')}
          </div>
          
          <div>
            <h4 style="color: #ffd56b; margin-bottom: 1rem;">Section Views</h4>
            ${Object.entries(sectionStats).map(([section, count]) => `
              <div style="margin-bottom: 0.5rem;">
                <div style="display: flex; justify-content: space-between;">
                  <span>${section}</span>
                  <span>${count} views</span>
                </div>
                <div class="progress-bar">
                  <div class="progress-fill" style="width: ${(count / Math.max(...Object.values(sectionStats))) * 100}%"></div>
                </div>
              </div>
            `).join('')}
          </div>
        `;
      }

      calculateStats() {
        const pageViews = this.data.filter(e => e.event === 'page_view');
        const timeEvents = this.data.filter(e => e.event === 'time_on_page');
        const interactions = this.data.filter(e => e.event === 'interaction');
        const projectViews = this.data.filter(e => e.event === 'project_view');
        const scrollEvents = this.data.filter(e => e.event === 'scroll_depth');

        return {
          totalEvents: this.data.length,
          uniqueSessions: pageViews.length,
          avgTimeOnPage: timeEvents.length ? Math.round(timeEvents.reduce((sum, e) => sum + (e.duration || 0), 0) / timeEvents.length) : 0,
          totalInteractions: interactions.length,
          projectViews: projectViews.length,
          avgScrollDepth: scrollEvents.length ? Math.round(scrollEvents.reduce((sum, e) => sum + (e.depth || 0), 0) / scrollEvents.length) : 0
        };
      }

      getDeviceStats() {
        const devices = {};
        this.data.filter(e => e.event === 'page_view').forEach(event => {
          const device = event.deviceType || 'unknown';
          devices[device] = (devices[device] || 0) + 1;
        });
        return devices;
      }

      getSectionStats() {
        const sections = {};
        this.data.filter(e => e.event === 'section_view').forEach(event => {
          const section = event.section || 'unknown';
          sections[section] = (sections[section] || 0) + 1;
        });
        return sections;
      }

      formatEventDetails(event) {
        switch(event.event) {
          case 'page_view':
            return `${event.deviceType} • ${event.viewport}`;
          case 'scroll_depth':
            return `Depth: ${event.depth}%`;
          case 'interaction':
            return `Element: ${event.element}`;
          case 'project_view':
            return `Project: ${event.project}`;
          case 'time_on_page':
            return `Duration: ${event.duration}s • Interactions: ${event.interactions}`;
          default:
            return JSON.stringify(event).slice(0, 100);
        }
      }

      refreshData() {
        this.loadData();
        this.renderStats();
        this.renderEvents();
        this.renderEngagementChart();
        console.log('📊 Dashboard refreshed');
      }

      exportData() {
        const dataStr = JSON.stringify(this.data, null, 2);
        const dataBlob = new Blob([dataStr], {type: 'application/json'});
        const url = URL.createObjectURL(dataBlob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `portfolio-analytics-${new Date().toISOString().split('T')[0]}.json`;
        link.click();
        URL.revokeObjectURL(url);
      }

      clearData() {
        if (confirm('Are you sure you want to clear all analytics data?')) {
          localStorage.removeItem('portfolio_analytics');
          this.data = [];
          this.refreshData();
          alert('Analytics data cleared!');
        }
      }
    }

    // Initialize dashboard
    window.refreshData = () => dashboard.refreshData();
    window.exportData = () => dashboard.exportData();
    window.clearData = () => dashboard.clearData();

    const dashboard = new AnalyticsDashboard();
  </script>
</body>
</html>
