<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Portfolio Test</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      background: #07070a;
      color: #eaeaf2;
      padding: 2rem;
      line-height: 1.6;
    }
    .test-section {
      background: #0c0f16;
      border: 1px solid rgba(255,255,255,0.1);
      border-radius: 12px;
      padding: 1.5rem;
      margin: 1rem 0;
    }
    .success { color: #27c93f; }
    .warning { color: #ffbd2e; }
    .error { color: #ff5f56; }
    .btn {
      background: linear-gradient(135deg, #ffd56b, #ff6b35);
      border: none;
      color: #07070a;
      padding: 0.8rem 1.5rem;
      border-radius: 8px;
      cursor: pointer;
      font-weight: 600;
      margin: 0.5rem;
    }
    .btn:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(255,213,107,0.3);
    }
  </style>
</head>
<body>
  <h1>🚀 Space Phoenix Portfolio - Test Page</h1>
  
  <div class="test-section">
    <h2>✅ Basic Functionality Test</h2>
    <p>If you can see this page with proper styling, the basic setup is working!</p>
    <button class="btn" onclick="testBasicJS()">Test JavaScript</button>
    <div id="js-test-result"></div>
  </div>
  
  <div class="test-section">
    <h2>🔗 Navigation Test</h2>
    <p>Test the main portfolio:</p>
    <a href="index.html" class="btn">Open Main Portfolio</a>
  </div>
  
  <div class="test-section">
    <h2>📱 Responsive Test</h2>
    <p>Resize your browser window to test responsive design.</p>
    <div id="viewport-info"></div>
  </div>
  
  <div class="test-section">
    <h2>🎨 Animation Test</h2>
    <div id="animation-box" style="width: 100px; height: 100px; background: linear-gradient(45deg, #ffd56b, #ff6b35); border-radius: 50%; margin: 1rem 0; transition: transform 0.3s ease;"></div>
    <button class="btn" onclick="testAnimation()">Test Animation</button>
  </div>

  <script>
    function testBasicJS() {
      const result = document.getElementById('js-test-result');
      result.innerHTML = '<span class="success">✅ JavaScript is working!</span>';
      
      // Test modern JS features
      try {
        const testArray = [1, 2, 3];
        const doubled = testArray.map(x => x * 2);
        const hasModernJS = doubled.length === 3;
        
        if (hasModernJS) {
          result.innerHTML += '<br><span class="success">✅ Modern JavaScript features supported</span>';
        }
      } catch (e) {
        result.innerHTML += '<br><span class="warning">⚠️ Some modern JS features may not work</span>';
      }
    }
    
    function testAnimation() {
      const box = document.getElementById('animation-box');
      box.style.transform = 'scale(1.2) rotate(45deg)';
      
      setTimeout(() => {
        box.style.transform = 'scale(1) rotate(0deg)';
      }, 500);
    }
    
    function updateViewportInfo() {
      const info = document.getElementById('viewport-info');
      info.innerHTML = `
        <span class="success">Viewport: ${window.innerWidth} × ${window.innerHeight}</span><br>
        <span class="success">Device Pixel Ratio: ${window.devicePixelRatio}</span><br>
        <span class="success">User Agent: ${navigator.userAgent.includes('Mobile') ? 'Mobile' : 'Desktop'}</span>
      `;
    }
    
    // Initialize
    updateViewportInfo();
    window.addEventListener('resize', updateViewportInfo);
    
    // Test console
    console.log('🧪 Test page loaded successfully');
    console.log('📊 Viewport:', window.innerWidth, '×', window.innerHeight);
    console.log('🔧 User Agent:', navigator.userAgent);
  </script>
</body>
</html>
